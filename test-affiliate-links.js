// Test script to verify the refactored affiliate link system
const { 
  findAffiliateLinksFromTags, 
  findMultipleAffiliateLinks, 
  AFFILIATE_LINK_TAGS 
} = require('./lib/affiliate-links-map.ts');

console.log('Testing Affiliate Link System Refactoring...\n');

// Test 1: Exact tag matching
console.log('Test 1: Exact tag matching');
const testTags1 = ['flight', 'booking'];
const exactMatches = findAffiliateLinksFromTags(testTags1, 2);
console.log(`Input tags: ${testTags1.join(', ')}`);
console.log(`Found ${exactMatches.length} exact matches:`);
exactMatches.forEach((link, index) => {
  console.log(`  ${index + 1}. ${link.title} (${link.provider})`);
});
console.log('');

// Test 2: Multiple affiliate links with tags
console.log('Test 2: Multiple affiliate links with AI-generated tags');
const testTags2 = ['accommodation', 'hotel', 'booking'];
const multipleMatches = findMultipleAffiliateLinks(
  'Book Hotel',
  'Find and book accommodation for your trip',
  2,
  'Paris',
  testTags2
);
console.log(`Input tags: ${testTags2.join(', ')}`);
console.log(`Found ${multipleMatches.length} matches:`);
multipleMatches.forEach((link, index) => {
  console.log(`  ${index + 1}. ${link.title} (${link.provider})`);
});
console.log('');

// Test 3: Fallback to text-based matching
console.log('Test 3: Fallback to text-based matching (no tags)');
const textMatches = findMultipleAffiliateLinks(
  'Book Restaurant',
  'Make dinner reservations at local restaurants',
  2,
  'Tokyo'
);
console.log(`Found ${textMatches.length} text-based matches:`);
textMatches.forEach((link, index) => {
  console.log(`  ${index + 1}. ${link.title} (${link.provider})`);
});
console.log('');

// Test 4: Available affiliate link tags
console.log('Test 4: Available affiliate link tags');
console.log(`Total available tags: ${AFFILIATE_LINK_TAGS.length}`);
console.log('Sample tags:', AFFILIATE_LINK_TAGS.slice(0, 10).join(', '), '...');

console.log('\nTesting completed!');
